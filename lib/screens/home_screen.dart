import 'dart:io';
import 'package:bitacora_template_generator/services/ai_service.dart';
import 'package:bitacora_template_generator/widgets/prompt_input_widget.dart';
import 'package:bitacora_template_generator/widgets/generated_json_display_widget.dart';
import 'package:flutter/material.dart';

enum GenerationState {
  promptInput,
  generatingJson,
  jsonGenerated,
  generatingHtml,
  htmlGenerated,
  error,
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  String? _currentJsonTemplate;
  GenerationState _state = GenerationState.promptInput;
  late AnimationController _buttonAnimationController;

  @override
  void initState() {
    super.initState();
    _buttonAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    // _state = GenerationState.generatingJson;
  }

  @override
  void dispose() {
    _buttonAnimationController.dispose();
    super.dispose();
  }

  Future<void> _onPromptSubmitted(String prompt, File? attachedFile) async {
    setState(() {
      _state = GenerationState.generatingJson;
    });

    _buttonAnimationController.forward().then((_) {
      _buttonAnimationController.reverse();
    });

    try {
      final jsonTemplate =
          await AiService.generateJsonFromPrompt(prompt, attachedFile);

      if (mounted) {
        if (jsonTemplate != null && jsonTemplate.isNotEmpty) {
          setState(() {
            _currentJsonTemplate = jsonTemplate;
            _state = GenerationState.jsonGenerated;
          });
        } else {
          setState(() {
            _state = GenerationState.error;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _state = GenerationState.error;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Stack(
        children: [
          CustomScrollView(
            slivers: [
              SliverToBoxAdapter(
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 24,
                  ),
                  child: _buildNewDesktopLayout(theme),
                ),
              ),
            ],
          ),

          // Floating loading overlay
          if (_state == GenerationState.generatingJson)
            _buildFloatingLoadingOverlay(theme),
        ],
      ),
    );
  }

  Widget _buildNewDesktopLayout(ThemeData theme) {
    final screenHeight = MediaQuery.of(context).size.height;
    final availableHeight =
        screenHeight - 280; // Account for app bar and padding

    return Center(
      child: Container(
        constraints: BoxConstraints(maxWidth: 800),
        height: availableHeight,
        child: Column(
          children: [
            if (_state == GenerationState.promptInput ||
                _state == GenerationState.generatingJson) ...[
              Expanded(
                child: PromptInputWidget(
                  onPromptSubmitted: _onPromptSubmitted,
                ),
              ),
            ] else if (_state == GenerationState.jsonGenerated &&
                _currentJsonTemplate != null) ...[
              Expanded(
                child: GeneratedJsonDisplayWidget(
                  jsonString: _currentJsonTemplate!,
                  onClear: () {
                    setState(() {
                      _currentJsonTemplate = null;
                      _state = GenerationState.promptInput;
                    });
                  },
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingLoadingOverlay(ThemeData theme) {
    return Container(
      color: Colors.black.withValues(alpha: 0.3),
      child: Center(
        child: Card(
          elevation: 8,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            padding: const EdgeInsets.all(32),
            constraints: const BoxConstraints(maxWidth: 300),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 48,
                  height: 48,
                  child: CircularProgressIndicator(
                    strokeWidth: 4,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      theme.colorScheme.primary,
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  'Generando JSON...',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'La IA está analizando tu descripción',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
